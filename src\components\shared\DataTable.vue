<template>
  <div class="flex flex-col p-5">
    <!-- 表格容器 -->
    <div class="bg-white rounded-lg shadow-sm">
      <!-- 表头 -->
      <div
        class="grid rounded overflow-hidden text-16px leading-22px border border-solid border-#A9C0EA text-#6D84AE bg-#E4EBF8"
        :style="{ gridTemplateColumns: getGridTemplateColumns() }"
      >
        <div
          v-for="(column, index) in columns"
          :key="column.key"
          class="px-4 py-2.5 cursor-pointer flex items-center"
          @click="handleSort(column)"
        >
          <span :style="{ textAlign: column.align || 'left' }">
            {{ column.title }}
          </span>
          <div v-if="column.sortable" class="flex flex-col ml-2">
            <span class="text-xs leading-none" :class="getSortIconClass(column, 'asc')"> ▲ </span>
            <span class="text-xs leading-none" :class="getSortIconClass(column, 'desc')"> ▼ </span>
          </div>
        </div>
      </div>

      <!-- 表格内容 -->
      <n-scrollbar :style="{ maxHeight: tableHeight }" class="mt-2">
        <div
          v-for="(item, index) in data"
          :key="index"
          class="grid py-2.25 mb-2.5 rounded"
          :class="[item.isHighlighted ? 'bg-red-50' : 'bg-[#F8F8F8] hover:bg-red-50']"
          :style="{ gridTemplateColumns: getGridTemplateColumns() }"
        >
          <div
            v-for="(column, colIndex) in columns"
            :key="column.key"
            class="px-4 flex items-center text-20px"
            :style="{
              color: '#5F6673',
              textAlign: column.align || 'left',
            }"
          >
            <slot
              :name="column.key"
              :item="item"
              :value="getColumnValue(item, column.key)"
              :index="index"
            >
              {{ getColumnValue(item, column.key) }}
            </slot>
          </div>
        </div>
      </n-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { NScrollbar } from 'naive-ui'

// 定义接口
interface TableColumn {
  key: string
  title: string
  sortable?: boolean
  width?: string
  align?: 'left' | 'center' | 'right'
}

interface SortState {
  key: string
  order: 'asc' | 'desc' | null
}

// 定义 props
interface Props {
  columns: TableColumn[]
  data: Record<string, any>[]
  height?: string
}

const props = withDefaults(defineProps<Props>(), {
  height: '400px',
})

// 定义 emits
const emit = defineEmits<{
  sort: [column: TableColumn, order: 'asc' | 'desc' | null]
}>()

// 响应式数据
const sortState = ref<SortState>({ key: '', order: null })

// 计算属性
const tableHeight = computed(() => props.height)

// 方法
const getColumnValue = (item: Record<string, any>, key: string) => {
  return item[key] || ''
}

const getGridTemplateColumns = () => {
  return props.columns
    .map((column) => {
      if (column.width) {
        return column.width
      }
      return '1fr'
    })
    .join(' ')
}

const handleSort = (column: TableColumn) => {
  if (!column.sortable) return

  if (sortState.value.key === column.key) {
    // 同一列：null -> asc -> desc -> null
    if (sortState.value.order === null) {
      sortState.value.order = 'asc'
    } else if (sortState.value.order === 'asc') {
      sortState.value.order = 'desc'
    } else {
      sortState.value.order = null
    }
  } else {
    // 不同列：重置为 asc
    sortState.value.key = column.key
    sortState.value.order = 'asc'
  }

  emit('sort', column, sortState.value.order)
}

const getSortIconClass = (column: TableColumn, direction: 'asc' | 'desc') => {
  if (sortState.value.key !== column.key) {
    return 'text-gray-400'
  }

  if (sortState.value.order === direction) {
    return 'text-blue-600'
  }

  return 'text-gray-400'
}
</script>

<style scoped></style>
