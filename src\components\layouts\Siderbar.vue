<template>
  <n-flex vertical class="h-100vh bg-#3075F6FF">
    <Logo></Logo>
    <n-layout-sider :native-scrollbar="false" width="338px">
      <n-menu
        :default-value="menuOptions[0].key"
        :options="menuOptions"
        @update:expanded-keys="handleUpdateExpandedKeys"
      />
    </n-layout-sider>
  </n-flex>
</template>

<script setup lang="ts">
import { NLayoutSider, NMenu, NFlex, NIcon } from 'naive-ui'
import Logo from '@/components/layouts/Logo.vue'
import { h, type Component } from 'vue'

/**
 * 一级标题
 * 二级标题
 * 三级标题
 */
const textClassOptions = {
  firstLevel: 'text-xl font-bold',
  secondLevel: 'text-xl font-400',
  thirdLevel: 'text-base font-normal',
}

/**
 * 文本 class名称
 */
function renderText(text: string, textLevel: keyof typeof textClassOptions) {
  return () =>
    h(
      'span',
      { class: textClassOptions[textLevel] || textClassOptions.firstLevel },
      { default: () => text },
    )
}

const menuOptions = [
  {
    label: renderText('断面监视', 'firstLevel'),
    key: 'section-monitoring',
  },
  {
    label: renderText('执行情况', 'firstLevel'),
    key: 'execution-situation',
    children: [
      {
        label: renderText('参与市场', 'secondLevel'),
        key: 'participate-market',
      },
      {
        label: renderText('煤炭', 'secondLevel'),
        key: 'coal',
        children: [
          {
            label: renderText('全省', 'thirdLevel'),
            key: 'all-province',
          },
          {
            label: renderText('江南', 'thirdLevel'),
            key: 'jiangnan',
          },
          {
            label: renderText('江北', 'thirdLevel'),
            key: 'jiangbei',
          },
        ],
      },
      {
        label: renderText('核电', 'secondLevel'),
        key: 'nuclear-power',
      },
      {
        label: renderText('风电', 'secondLevel'),
        key: 'wind-power',
      },
      {
        label: renderText('光伏', 'secondLevel'),
        key: 'photovoltaic',
      },
      {
        label: renderText('燃煤', 'secondLevel'),
        key: 'coal-fired',
      },
      {
        label: renderText('燃气', 'secondLevel'),
        key: 'gas',
      },
      {
        label: renderText('抽蓄', 'secondLevel'),
        key: 'pumped-storage',
      },
      {
        label: renderText('独立蓄能', 'secondLevel'),
        key: 'independent-storage',
      },
      {
        label: renderText('不参与市场', 'secondLevel'),
        key: 'not-participate-market',
      },
      {
        label: renderText('其他', 'secondLevel'),
        key: 'other',
      },
    ],
  },
  {
    label: renderText('负荷预测', 'firstLevel'),
    key: 'load-forecasting',
    children: [
      {
        label: renderText('统调负荷', 'secondLevel'),
        key: 'unified-scheduling-load',
      },
      {
        label: renderText('母线负荷', 'secondLevel'),
        key: 'busbar-load',
      },
    ],
  },
]

const handleUpdateExpandedKeys = (keys: string[]) => {
  console.log(keys)
}
</script>
