<template>
  <component :is="!!executionSituationStore.selectedTableRow ? TableInfo : TableView"></component>
</template>

<script setup lang="ts">
import { useExecutionSituationStore } from '@/stores/'

import TableView from '@/components/features/section-monitoring/overview/SectionStatistic.vue'
import TableInfo from '@/components/features/section-monitoring/info/index.vue'

const executionSituationStore = useExecutionSituationStore()
</script>
