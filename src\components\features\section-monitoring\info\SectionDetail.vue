<template>
  <div class="flex bg-#FEF4E8 gap-4 rounded mx-7.5">
    <div
      v-for="(item, index) in sectionDetail"
      :key="index"
      class="flex flex-auto flex-col flex-grow-1 p-5"
    >
      <span class="text-#CC8F60 text-base font-normal flex items-center"
        ><span class="text-5xl mr-1">·</span> {{ item.label }}</span
      >
      <span class="text-#643F1A text-xl font-medium">{{ item.value }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const sectionDetail = ref({
  name: {
    label: '线路名称',
    value: '月溪5k11线，月溪5k12线双线',
  },
  volt: {
    label: '电压等级',
    value: '500kV',
  },
  // 统计时段
  time: {
    label: '统计时段',
    value: '2025-01-01 22:12:34',
  },
  // 最大越线潮流
  maxFlow: {
    label: '最大越线潮流',
    value: 'XXX',
  },
  // 越线最大值
  maxValue: {
    label: '越线最大值',
    value: 'XXX',
  },
  //  总越限时间
  totalOverLimitTime: {
    label: '总越限时间',
    value: 'XXX',
  },
  // 最长出线越线时长
  longestOverLimitTime: {
    label: '最长出线越线时长',
    value: 'XXX',
  },
})
</script>
